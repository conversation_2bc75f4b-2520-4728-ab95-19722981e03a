import type { PluginOption } from 'vite';

interface ResourcePreloaderOptions {
  /** 关键字体预加载 */
  criticalFonts?: string[];
  /** 关键图片预加载 */
  criticalImages?: string[];
  /** 自定义预加载资源列表 */
  customResources?: Array<{
    as: 'fetch' | 'font' | 'image' | 'script' | 'style';
    crossorigin?: boolean;
    href: string;
    media?: string;
  }>;
  /** DNS 预解析域名列表 */
  dnsPrefetchDomains?: string[];
  enabled?: boolean;
  /** 是否启用 DNS 预解析 */
  enableDnsPrefetch?: boolean;
  /** 是否启用模块预加载 */
  enableModulePreload?: boolean;
  /** 是否启用预连接 */
  enablePreconnect?: boolean;
  /** 预连接域名列表 */
  preconnectDomains?: string[];
  /** 预加载策略 */
  strategy?: 'all' | 'critical' | 'custom' | 'important';
}

interface ResourceHint {
  as?: string;
  crossorigin?: boolean;
  href: string;
  media?: string;
  rel: string;
  type?: string;
}

/**
 * 资源预加载优化插件
 * 自动生成资源预加载提示，优化页面加载性能
 */
export const viteResourcePreloaderPlugin = (
  options: ResourcePreloaderOptions = {},
): PluginOption => {
  const {
    criticalFonts = [],
    criticalImages = [],
    customResources = [],
    dnsPrefetchDomains = [
      'surgebot.io',
      'silkbot.org',
      'solana.surgebot.io',
      'fonts.googleapis.com',
      'fonts.gstatic.com',
    ],
    enabled = true,
    enableDnsPrefetch = true,
    enableModulePreload = true,
    enablePreconnect = true,
    preconnectDomains = [
      'https://surgebot.io',
      'https://silkbot.org',
      'https://solana.surgebot.io',
    ],
    strategy = 'critical',
  } = options;

  if (!enabled) {
    return null;
  }

  let resolvedConfig: any;
  let analyzedResources: ResourceHint[] = [];

  return {
    apply: 'build',
    configResolved(config) {
      resolvedConfig = config;
    },

    generateBundle(_, bundle) {
      // 分析构建产物，生成预加载提示
      analyzedResources = analyzeBundle(bundle, strategy);
    },

    name: 'vite:resource-preloader',

    transformIndexHtml: {
      enforce: 'pre',
      transform(html) {
        const hints: string[] = [];

        // DNS 预解析
        if (enableDnsPrefetch) {
          dnsPrefetchDomains.forEach((domain) => {
            hints.push(`<link rel="dns-prefetch" href="//${domain}">`);
          });
        }

        // 预连接
        if (enablePreconnect) {
          preconnectDomains.forEach((domain) => {
            hints.push(`<link rel="preconnect" href="${domain}" crossorigin>`);
          });
        }

        // 关键字体预加载
        criticalFonts.forEach((font) => {
          hints.push(
            `<link rel="preload" href="${font}" as="font" type="font/woff2" crossorigin>`,
          );
        });

        // 关键图片预加载
        criticalImages.forEach((image) => {
          hints.push(`<link rel="preload" href="${image}" as="image">`);
        });

        // 自定义资源预加载
        customResources.forEach((resource) => {
          const crossorigin = resource.crossorigin ? ' crossorigin' : '';
          const media = resource.media ? ` media="${resource.media}"` : '';
          hints.push(
            `<link rel="preload" href="${resource.href}" as="${resource.as}"${crossorigin}${media}>`,
          );
        });

        // 分析得出的资源预加载
        analyzedResources.forEach((resource) => {
          const attrs = [`rel="${resource.rel}"`, `href="${resource.href}"`];

          if (resource.as) attrs.push(`as="${resource.as}"`);
          if (resource.type) attrs.push(`type="${resource.type}"`);
          if (resource.crossorigin) attrs.push('crossorigin');
          if (resource.media) attrs.push(`media="${resource.media}"`);

          hints.push(`<link ${attrs.join(' ')}>`);
        });

        // 模块预加载脚本
        if (enableModulePreload) {
          hints.push(`
            <script>
              // 智能模块预加载
              (function() {
                const preloadModules = ${JSON.stringify(getModulePreloadList(strategy))};
                const isSupported = 'modulepreload' in HTMLLinkElement.prototype;
                
                if (isSupported) {
                  preloadModules.forEach(module => {
                    const link = document.createElement('link');
                    link.rel = 'modulepreload';
                    link.href = module;
                    document.head.appendChild(link);
                  });
                }
              })();
            </script>
          `);
        }

        // 在 head 标签中插入预加载提示
        if (hints.length > 0) {
          const hintsHtml = hints.join('\n    ');
          return html.replace('<head>', `<head>\n    ${hintsHtml}`);
        }

        return html;
      },
    },
  };
};

function analyzeBundle(bundle: any, strategy: string): ResourceHint[] {
  const hints: ResourceHint[] = [];
  const entries: string[] = [];
  const criticalChunks: string[] = [];
  const importantChunks: string[] = [];

  // 分析 bundle 中的文件
  Object.entries(bundle).forEach(([fileName, chunk]: [string, any]) => {
    if (chunk.type === 'chunk') {
      if (chunk.isEntry) {
        entries.push(fileName);
      }

      // 根据文件名判断重要性
      if (fileName.includes('vendor') || fileName.includes('vue')) {
        criticalChunks.push(fileName);
      } else if (fileName.includes('dashboard') || fileName.includes('swap')) {
        importantChunks.push(fileName);
      }
    }
  });

  // 根据策略生成预加载提示
  switch (strategy) {
    case 'all': {
      // 预加载所有资源
      Object.keys(bundle).forEach((fileName) => {
        if (bundle[fileName].type === 'chunk') {
          hints.push({
            as: fileName.endsWith('.css') ? 'style' : 'script',
            href: `./${fileName}`,
            rel: 'preload',
          });
        }
      });
      break;
    }

    case 'critical': {
      // 只预加载关键资源
      [...entries, ...criticalChunks].forEach((fileName) => {
        hints.push({
          as: fileName.endsWith('.css') ? 'style' : 'script',
          href: `./${fileName}`,
          rel: 'preload',
        });
      });
      break;
    }

    case 'important': {
      // 预加载重要资源
      [...entries, ...criticalChunks, ...importantChunks].forEach(
        (fileName) => {
          hints.push({
            as: fileName.endsWith('.css') ? 'style' : 'script',
            href: `./${fileName}`,
            rel: 'preload',
          });
        },
      );
      break;
    }
  }

  return hints;
}

function getModulePreloadList(strategy: string): string[] {
  const modules: string[] = [];

  switch (strategy) {
    case 'all': {
      modules.push(
        './src/bootstrap.ts',
        './src/router/index.ts',
        './src/stores/index.ts',
        './src/views/dashboard/index.vue',
        './src/views/swap/index.vue',
        './src/views/monitor/index.vue',
        './src/views/follow/index.vue',
      );
      break;
    }

    case 'critical': {
      modules.push(
        './src/bootstrap.ts',
        './src/router/index.ts',
        './src/stores/index.ts',
      );
      break;
    }

    case 'important': {
      modules.push(
        './src/bootstrap.ts',
        './src/router/index.ts',
        './src/stores/index.ts',
        './src/views/dashboard/index.vue',
        './src/views/swap/index.vue',
      );
      break;
    }
  }

  return modules;
}

/**
 * 创建资源预加载配置的辅助函数
 */
export function createResourcePreloaderConfig(
  options: Partial<ResourcePreloaderOptions> = {},
): ResourcePreloaderOptions {
  return {
    criticalFonts: [
      // 添加关键字体路径
    ],
    criticalImages: [
      '/logo.jpg',
      '/launch.jpg',
      // 添加其他关键图片
    ],
    customResources: [
      // 添加自定义预加载资源
    ],
    dnsPrefetchDomains: [
      'surgebot.io',
      'silkbot.org',
      'solana.surgebot.io',
      'fonts.googleapis.com',
      'fonts.gstatic.com',
      'unpkg.com',
      'cdn.jsdelivr.net',
    ],
    enabled: true,
    enableDnsPrefetch: true,
    enableModulePreload: true,
    enablePreconnect: true,
    preconnectDomains: [
      'https://surgebot.io',
      'https://silkbot.org',
      'https://solana.surgebot.io',
    ],
    strategy: 'critical',
    ...options,
  };
}
