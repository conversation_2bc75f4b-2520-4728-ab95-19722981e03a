import type { PluginOption } from 'vite';

import fs from 'node:fs';
import path from 'node:path';

interface SmartPreloadOptions {
  /**
   * 自定义预加载路由列表
   */
  customRoutes?: string[];
  /**
   * 预加载延迟时间（毫秒）
   */
  delay?: number;
  enabled?: boolean;
  /**
   * 网络条件限制
   */
  networkConditions?: {
    /**
     * 是否在数据保存模式下禁用预加载
     */
    disableOnDataSaver?: boolean;
    /**
     * 是否在慢速网络下禁用预加载
     */
    disableOnSlowNetwork?: boolean;
  };
  /**
   * 是否在空闲时预加载
   */
  onIdle?: boolean;
  /**
   * 预加载策略
   * - 'critical': 只预加载关键路由
   * - 'popular': 预加载热门路由
   * - 'all': 预加载所有路由
   * - 'custom': 自定义预加载列表
   */
  strategy?: 'all' | 'critical' | 'custom' | 'popular';
}

interface RouteInfo {
  chunkName?: string;
  component: string;
  path: string;
  priority: number;
}

/**
 * 智能预加载插件
 * 根据路由优先级和网络条件智能预加载资源
 */
export const viteSmartPreloadPlugin = (
  options: SmartPreloadOptions = {},
): PluginOption => {
  const {
    customRoutes = [],
    delay = 2000,
    enabled = true,
    networkConditions = {
      disableOnDataSaver: true,
      disableOnSlowNetwork: true,
    },
    onIdle = true,
    strategy = 'critical',
  } = options;

  if (!enabled) {
    return null;
  }

  let resolvedConfig: any;
  let routeAnalysis: RouteInfo[] = [];

  return {
    apply: 'build',
    buildStart() {
      // 分析路由文件，提取路由信息
      routeAnalysis = analyzeRoutes(resolvedConfig.root);
    },

    configResolved(config) {
      resolvedConfig = config;
    },

    generateBundle() {
      // 生成预加载脚本
      const preloadScript = generatePreloadScript(routeAnalysis, {
        customRoutes,
        delay,
        networkConditions,
        onIdle,
        strategy,
      });

      // 将预加载脚本作为资源添加到 bundle
      this.emitFile({
        fileName: 'smart-preload.js',
        source: preloadScript,
        type: 'asset',
      });
    },

    name: 'vite:smart-preload',

    transformIndexHtml: {
      enforce: 'post',
      transform(html) {
        // 在 HTML 中注入预加载脚本
        const scriptTag = '<script src="./smart-preload.js" defer></script>';
        return html.replace('</head>', `  ${scriptTag}\n</head>`);
      },
    },
  };
};

function analyzeRoutes(rootDir: string): RouteInfo[] {
  const routes: RouteInfo[] = [];
  const routesDir = path.join(rootDir, 'src/router/routes/modules');

  if (!fs.existsSync(routesDir)) {
    return routes;
  }

  const routeFiles = fs
    .readdirSync(routesDir)
    .filter((file) => file.endsWith('.ts'));

  routeFiles.forEach((file) => {
    const filePath = path.join(routesDir, file);
    const content = fs.readFileSync(filePath, 'utf8');

    // 简单的路由解析（实际项目中可能需要更复杂的 AST 解析）
    const pathMatches = content.match(/path:\s*['"`]([^'"`]+)['"`]/g);
    const componentMatches = content.match(
      /component:\s*\(\)\s*=>\s*import\(['"`]([^'"`]+)['"`]\)/g,
    );

    if (pathMatches && componentMatches) {
      pathMatches.forEach((pathMatch, index) => {
        const path = pathMatch.match(/['"`]([^'"`]+)['"`]/)?.[1];
        const componentMatch = componentMatches[index];
        const component = componentMatch?.match(/['"`]([^'"`]+)['"`]/)?.[1];

        if (path && component) {
          // 根据路由路径确定优先级
          let priority = 3; // 默认优先级

          if (path === '/' || path.includes('dashboard')) {
            priority = 1; // 最高优先级
          } else if (path.includes('swap') || path.includes('monitor')) {
            priority = 2; // 高优先级
          }

          routes.push({
            chunkName: path.replaceAll(/[^a-z0-9]/gi, '-'),
            component,
            path,
            priority,
          });
        }
      });
    }
  });

  return routes.sort((a, b) => a.priority - b.priority);
}

function generatePreloadScript(
  routes: RouteInfo[],
  options: {
    customRoutes: string[];
    delay: number;
    networkConditions: any;
    onIdle: boolean;
    strategy: string;
  },
): string {
  const { customRoutes, delay, networkConditions, onIdle, strategy } = options;

  // 根据策略筛选要预加载的路由
  let routesToPreload: RouteInfo[] = [];

  switch (strategy) {
    case 'all': {
      routesToPreload = routes;
      break;
    }
    case 'critical': {
      routesToPreload = routes.filter((route) => route.priority <= 2);
      break;
    }
    case 'custom': {
      routesToPreload = routes.filter((route) =>
        customRoutes.some((customRoute) => route.path.includes(customRoute)),
      );
      break;
    }
    case 'popular': {
      routesToPreload = routes.filter((route) => route.priority <= 3);
      break;
    }
  }

  return `
(function() {
  'use strict';
  
  // 网络条件检测
  function checkNetworkConditions() {
    ${
      networkConditions.disableOnSlowNetwork
        ? `
    if (navigator.connection) {
      const connection = navigator.connection;
      // 检测慢速网络
      if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
        return false;
      }
      // 检测数据保存模式
      if (connection.saveData && ${networkConditions.disableOnDataSaver}) {
        return false;
      }
    }
    `
        : ''
    }
    return true;
  }
  
  // 预加载函数
  function preloadRoute(componentPath) {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'modulepreload';
      link.href = componentPath;
      link.onload = resolve;
      link.onerror = reject;
      document.head.appendChild(link);
    });
  }
  
  // 批量预加载
  function batchPreload(routes, batchSize = 2) {
    let index = 0;
    
    function loadBatch() {
      const batch = routes.slice(index, index + batchSize);
      if (batch.length === 0) return;
      
      const promises = batch.map(route => {
        console.log('[Smart Preload] Preloading:', route.component);
        return preloadRoute(route.component).catch(err => {
          console.warn('[Smart Preload] Failed to preload:', route.component, err);
        });
      });
      
      Promise.all(promises).then(() => {
        index += batchSize;
        if (index < routes.length) {
          // 延迟加载下一批
          setTimeout(loadBatch, 100);
        }
      });
    }
    
    loadBatch();
  }
  
  // 主预加载逻辑
  function startPreloading() {
    if (!checkNetworkConditions()) {
      console.log('[Smart Preload] Skipped due to network conditions');
      return;
    }
    
    const routesToPreload = ${JSON.stringify(routesToPreload)};
    
    if (routesToPreload.length === 0) {
      console.log('[Smart Preload] No routes to preload');
      return;
    }
    
    console.log('[Smart Preload] Starting preload for', routesToPreload.length, 'routes');
    batchPreload(routesToPreload);
  }
  
  // 启动预加载
  ${
    onIdle
      ? `
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      setTimeout(startPreloading, ${delay});
    });
  } else {
    setTimeout(startPreloading, ${delay});
  }
  `
      : `
  setTimeout(startPreloading, ${delay});
  `
  }
  
  // 导出到全局，方便调试
  window.__smartPreload = {
    routes: ${JSON.stringify(routesToPreload)},
    preloadRoute,
    startPreloading,
  };
})();
  `;
}
